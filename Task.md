You are the solo architect & developer.  
Goal: deliver a runnable **local prototype** of an inventory + sales system for
a leather warehouse / shop.  Users can add leather stock in kilograms, record
sales, see automatic cost / profit, and attach rich metadata for future
products (shoes, clothing, bags).

────────────────────────────────────────────────────────────────────────────
1️⃣  DEV ENVIRONMENT ASSUMPTIONS
────────────────────────────────────────────────────────────────────────────
• OS……….. Windows 10/11, VS 2022 or VS Code + C# extension  
• SDK………. .NET 8 (LTS)  
• DB……….. Local SQL Server installed on machine
   • Integrated Authentication  
   • DB name:  InventoryDb  (you will need to create this)
• ORMs……. EF Core 8, Code-First  
• Frontend… Blazor WebAssembly Hosted (ASP.NET Core Hosted template)  
• Auth…….. NONE for now (simple local prototype).  Roles will be added later.  
• AI ………  Stubbed: a local service returns a fake parsed-receipt JSON so the
            rest of the flow works without Azure or file storage.

────────────────────────────────────────────────────────────────────────────
2️⃣  FUNCTIONAL SCOPE  (MVP)
────────────────────────────────────────────────────────────────────────────
■ **Inventory (Leather by kg)**
   - Add / edit leather type  
     • fields: Id, Name, Category (Goat, Cow, Buffalo, Sheep…), Thickness (mm),
       Grade, Colour, CostPerKg (decimal), CurrentQtyKg  
   - Receive stock → increases qty & records CostPerKg (can vary per batch)  
   - Reorder alert threshold (kg)  

■ **Sales**
   - Manual sale form  
     • choose Leather Type  
     • quantity kg, SalePricePerKg  
     • auto-compute revenue, cost, profit  
   - *Optional stub*: “Import sale from receipt” – call fake service that
     returns {leatherId, qty, salePrice, date}.  (No file upload yet.)

■ **Metadata for Future Articles**
   - Separate table **ArticleMeta** with flexible key/value pairs OR
     a JSON column in Leather table (your choice).  Example:
     `{ "IdealFor": ["Shoes", "Belts"], "Finish": "Nappa" }`

■ **Reports**
   - Dashboard (Today’s Sales ₹, Profit ₹, Low-Stock list)  
   - Sales log table  
   - Export Sales & Inventory as CSV (local file download)
   - Various Inventory reports, with age, money locked  and othe fancy stuff

────────────────────────────────────────────────────────────────────────────
3️⃣  HIGH-LEVEL TASK LIST  (Claude executes sequentially)
────────────────────────────────────────────────────────────────────────────

### 🧩 TASK 1 – Solution Setup
1.1  Create a new **Blazor WASM Hosted** solution `LeatherInv`.  
1.2  Add projects: `Domain`, `Infrastructure`, `Application`, `WebApi`,
     `WebClient`.  Use clean-architecture folder layout.

### 📐 TASK 2 – Domain & EF Core
2.1  Define entities:  
     *Leather*, *StockBatch*, *Sale*, *SaleLine* (if you want multiple lines),  
     *ArticleMeta* (or JSON field), *AuditLog* (for later).  
2.2  Configure `LeatherInventoryDbContext`; enable
     • Integrated Security connection string  
     • SQL Server provider  
2.3  Add initial **EF migration** & update-database.  
2.4  Insert **Seed Data**:  
     - 3 leather types (Cow Full-Grain, Goat Suede, Buffalo Pull-Up)  
     - 2 stock batches for each type with different cost/kg & quantities  
     - A couple of sample sales.  

### 🔌 TASK 3 – Minimal APIs
3.1  Endpoints:  
     `GET /api/leather`  
     `POST /api/leather`  
     `POST /api/stock-in`  
     `POST /api/sale/manual`  
     `POST /api/sale/from-receipt`   ← calls stub parser  
     `GET /api/reports/dashboard`  
3.2  Use **MediatR** + **FluentValidation** for commands.

### 🤖 TASK 4 – Stub Receipt Parser
4.1  Create service `IReceiptParser` with method  
     `Task<ParsedSaleDto> ParseAsync(string fakeImageId)`  
     It returns hard-coded JSON (simulate extracted fields).  
4.2  Wire `/sale/from-receipt` to call this service.

### 🎨 TASK 5 – Blazor UI
5.1  Design mobile-first pages:  
     • Dashboard.  • Leather List & Add Form.  
     • Stock-In Form.  • Sale Form.  
     • “Import From Receipt” button that calls stub and shows preview.  
5.2  Use **MudBlazor** or **Radzen** for quick components.  
5.3  Show toast notifications on success/fail.

### ⚙️ TASK 6 – CSV Export
6.1  Utility to generate CSV from sales & inventory; return as `File` result.  
6.2  Add “Download” buttons on UI.

### 🧪 TASK 7 – Tests
7.1  xUnit tests for:  
     • Stock deduction logic  
     • Profit calculation  
     • Seed data correctness  

### 🔄 TASK 8 – README & Local Run Script
8.1  Write README with:  
     - Prereqs (SDK, SQL Server, how to enable LocalDB)  
     - `dotnet ef database update`  
     - `dotnet run` instructions  
8.2  Optionally create `launchSettings.json` to auto-start WebApi & WASM.

────────────────────────────────────────────────────────────────────────────
4️⃣  EXECUTION GUIDELINES  (for Claude)
────────────────────────────────────────────────────────────────────────────
• Present each sub-task under an `### ✅ TASK X.Y` header.  
• Include the **actual code** (C# classes, migrations, razor pages, etc.).  
• For long code blocks, use triple backticks and proper language tags.  

────────────────────────────────────────────────────────────────────────────
5️⃣  OPTIONAL QUESTIONS BEFORE START
────────────────────────────────────────────────────────────────────────────
1. Do we need **unit of measure** other than kg  - yes (Number of units for shoes etc))
2. Should we include **GST fields** now or later?  (now)
3. Is **English-only UI** fine for the prototype?  (yes)
4. Any preference between **MudBlazor** and **Radzen**? You analyze and decide

If answers are unclear, assume: only kg, skip GST for now, English-only, use MudBlazor.

Begin with **TASK 1 – Solution Setup**.
